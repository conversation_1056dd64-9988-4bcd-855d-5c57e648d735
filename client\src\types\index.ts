// Re-export all types from the auto-generated file
export * from './generated';

// Legacy compatibility exports - these will be removed in future versions
// Please migrate to using the types from './generated' directly
import type {
  User,
  Post as GeneratedPost,
  Comment as GeneratedComment,
  PostWithAuthor,
  CommentWithAuthor,
  PostStatus,
  SortOrder,
  SortOptions,
  PostFilters,
  UserFilters,
  CreateUserInput,
  UpdateUserInput,
  CreatePostInput,
  UpdatePostInput,
  CreateCommentInput,
  UpdateCommentInput,
  CreateCategoryInput,
  UpdateCategoryInput,
} from './generated';

/**
 * @deprecated Use User from './generated' instead
 */
export type Author = User;

/**
 * @deprecated Use PostWithAuthor from './generated' instead
 */
export interface Post extends Omit<GeneratedPost, 'authorId'> {
  author: User;
  comments: CommentWithAuthor[];
}

/**
 * @deprecated Use CommentWithAuthor from './generated' instead
 */
export interface Comment extends Omit<GeneratedComment, 'authorId' | 'postId'> {
  author: User;
}
